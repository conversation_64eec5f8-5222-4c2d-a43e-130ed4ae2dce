// 测试活动配置接口调用
// 这个文件用于验证接口是否正常工作

// 模拟接口响应数据结构
const mockApiResponse = {
  code: "00",
  body: {
    // 活动key、路由 key
    key: "xinHua2026",
    // 院校id
    unvsId: "168984905050474751",
    // 院校名称
    schoolName: "广州新华学院",
    // 活动主图
    bannerImg: "scholarship/xinHua2026/banner.png",
    // 主页优惠券图
    couponImg: "scholarship/xinHua2026/coupon.png",
    // 院校logo
    logo: "scholarship/xinHua2026/logo.png",
    // 未激活优惠券图
    unActiveCouponImg: "scholarship/xinHua2026/un-active-coupon.png",
    // 激活后优惠券图
    activeCouponImg: "scholarship/xinHua2026/active-coupon.png",
    // 未激活礼包图
    unActivePacket: "scholarship/xinHua2026/un-active-packet.png",
    // 激活礼包图
    activePacket: "scholarship/xinHua2026/active-packet.png",
    // 奖学金挑战成功优惠券获得金额
    scholarshipAmountText: "3000",
    // 分享文案
    shareTitle: "3000元奖学金报读活动，限时参加！",
    // 活动最晚开启时间,仅前端展示，不做限制
    latestDareOpenTime: "2026.07.17",
    scholarship1680: "244", // 分享和报读时候， 使用1680元的奖学金的优惠类型
    scholarship599: "243", // 请求活动信息的时候，使用599元的奖学金的优惠类型
    actName: "1680上进21奖学金", // 报读类型使用1680优惠类型的文案
    remark: "", // 报读主页底部-备注
    majors: [
      {
        category: '经管类 (专升本)',
        items: [
          { name: ['人力资源管理', '国际经济与贸易', '会计学', '工商管理', '行政管理'], fee: '3500.00元/学年', miscellaneous: '400.00元/学年', duration: '2.5年' }
        ]
      },
      {
        category: '理工类 (专升本)',
        items: [
          { name: ['数字媒体技术', '软件工程', '电气工程及其自动化'], fee: '3500.00元/学年', miscellaneous: '400.00元/学年', duration: '2.5年' }
        ]
      },
      {
        category: '医学类 (专升本)',
        items: [
          { name: ['护理学'], fee: '4000.00元/学年', miscellaneous: '400.00元/学年', duration: '2.5年' }
        ]
      }
    ],
    // 奖学金规则
    scholarshipRules: `
      <h3>一、在<b>2025年07月17日10点</b>起，招满即止，期间成功报读<b>2026级广州新华学院</b>的学员（以<b>缴纳599元或1680元</b>购买学习大礼包为准），可额外获得<b>3000元奖学金</b>抵扣券。</h3>
      <br />
      <h3>二、奖学金发放说明：</h3>
      <p>（1）限购买599元或者1680元学习大礼包的学员参与。</p>
      <p>（2）在自报读之日起，完成累计21天的跑步打卡以及参与远智21天上进训练营后，方可激活奖学金。</p>
      <p>（3）仅限抵减成教<b>2026级广州新华学院第2年、第2.5年等值学费</b>。</p>
    `,
    // 活动规则
    rules: `
      <h1 class="title-bg">活动背景</h1>
      <br />
      <p>为了鼓励更多的社会人士成为上进青年，不间断自己的学习步伐，经校办研究决定，在新的一年推出<b>广州新华学院</b>上进21奖学金计划，详情如下：</p>
      <br />
      <h3>一、在<b>2025年07月17日10点</b>起，招满即止，期间成功报读<b>2026级广州新华学院</b>的学员（以<b>缴纳599元或1680元</b>购买学习大礼包为准），可额外获得<b>3000元奖学金</b>抵扣券。</h3>
      <br />
      <h3>二、奖学金发放说明：</h3>
      <p>（1）限购买599元或者1680元学习大礼包的学员参与。</p>
      <p>（2）在自报读之日起，完成累计21天的跑步打卡以及参与远智21天上进训练营后，方可激活奖学金。</p>
      <p>（3）仅限抵减成教<b>2026级广州新华学院第2年、第2.5年等值学费</b>。</p>
    `
  }
}

// 验证数据结构是否完整
function validateConfigStructure(config) {
  const requiredFields = [
    'key', 'unvsId', 'schoolName', 'bannerImg', 'couponImg', 'logo',
    'unActiveCouponImg', 'activeCouponImg', 'unActivePacket', 'activePacket',
    'scholarshipAmountText', 'shareTitle', 'latestDareOpenTime',
    'scholarship1680', 'scholarship599', 'actName', 'majors',
    'scholarshipRules', 'rules'
  ]
  
  const missingFields = requiredFields.filter(field => !config.hasOwnProperty(field))
  
  if (missingFields.length > 0) {
    console.error('缺少必要字段:', missingFields)
    return false
  }
  
  console.log('配置数据结构验证通过')
  return true
}

// 测试接口调用
async function testApiCall() {
  try {
    console.log('测试接口调用: /mkt/getActivityConfig/1.0/')
    console.log('请求参数: { activitySign: "xinHua2026" }')
    
    // 模拟接口响应
    console.log('模拟接口响应:', mockApiResponse)
    
    // 验证响应数据结构
    if (mockApiResponse.code === "00" && mockApiResponse.body) {
      const isValid = validateConfigStructure(mockApiResponse.body)
      if (isValid) {
        console.log('✅ 接口调用测试通过')
        console.log('✅ 数据结构验证通过')
      } else {
        console.log('❌ 数据结构验证失败')
      }
    } else {
      console.log('❌ 接口响应格式错误')
    }
    
  } catch (error) {
    console.error('❌ 接口调用测试失败:', error)
  }
}

// 运行测试
console.log('开始测试活动配置接口...')
testApiCall()

console.log('\n=== 测试总结 ===')
console.log('1. 接口地址: https://192-pre.yzou.cn/proxy/mkt/getActivityConfig/1.0/')
console.log('2. 请求参数: {"activitySign":"xinHua2026"}')
console.log('3. 预期响应格式: { code: "00", body: {...} }')
console.log('4. 必要字段已验证')
console.log('5. 错误处理已实现')
